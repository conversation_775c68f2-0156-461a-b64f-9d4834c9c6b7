---
title: "N8N + Browser Use Cloud"
description: "Learn how to integrate Browser Use Cloud API with n8n using a practical workflow example (competitor research)."
icon: "plug"
---

> **TL;DR** – In **3 minutes** you can have an n8n workflow that:
>
> 1. Shows a form asking for a competitor’s name
> 2. Starts a Browser Use task that crawls the web and extracts **pricing, jobs, new features & announcements**
> 3. Waits for the task to finish via a **webhook**
> 4. Formats the output and drops a rich message into Slack

You can grab the workflow JSON below – copy it and import it into n8n, plug in your API keys and hit *Execute* 🚀.

---

## Why use Browser Use in n8n?

• **Autonomous browsing** – Browser Use opens pages like a real user (using Playwright + Chromium), follows links, clicks buttons and reads DOM content.

• **Structured output** – You tell the agent *exactly* which fields you need. No brittle regex or XPaths.

• **Scales effortlessly** – Kick off hundreds of tasks and monitor them through the Cloud API.

n8n glues everything together so your team gets the data instantly—no Python scripts or CRON jobs needed.

---

## Prerequisites

1. **Browser Use Cloud API key** – grab one from your [Billing page](https://cloud.browser-use.com/billing).
2. **n8n instance** – self-hosted or n8n.cloud. (The screenshots below use n8n 1.45+.)
3. **Slack Incoming Webhook URL** – create one in your Slack workspace.

Add both secrets to n8n’s credential manager:

```env title=".env example"
BROWSER_USE_API_KEY="sk-…"
SLACK_WEBHOOK_URL="https://hooks.slack.com/services/…"
```

---

## Import the template

1. Copy the [workflow JSON](#workflow-json) below to your clipboard.
2. In n8n click **Import** → **From Clipboard** and paste the JSON.
3. Hit **Import**
4. Replace the *Browser-Use API Key* credential and *Slack webhook URL* credential with yours.

---

## How the workflow works

### 1. `Form Trigger` – collect the competitor’s name
A public n8n form with a single required field. When a user submits, the workflow fires instantly.

### 2. `HTTP Request – Browser Use Run Task`
We POST to `/api/v1/run-task` with the following body:

```json title="run-task payload"
{
  "task": "Do exhaustive research on {{ $json[\"Competitor Name\"] }} and extract all pricing information, job postings, new features and announcements",
  "save_browser_data": true,
  "structured_output_json": {
    "pricing": {"plans": ["string"], "prices": ["string"], "features": ["string"]},
    "jobs": {"titles": ["string"], "departments": ["string"], "locations": ["string"]},
    "new_features": {"titles": ["string"], "description": ["string"]},
    "announcements": {"titles": ["string"], "description": ["string"]}
  },
  "metadata": {"source": "n8n-competitor-demo"}
}
```

Important bits:

• `structured_output_json` tells the agent which keys to return – no post-processing required.
• We tag the task with `metadata.source` so the webhook can filter only *our* jobs.

### 3. `Webhook` + `IF` – wait for task completion
Browser Use sends a webhook when anything happens to a task (see our [Webhooks guide](/cloud/webhooks) for setup details). We expose an n8n Webhook node at `/get-research-data` and let the agent call it.

We only proceed when **both** conditions are true:

* `payload.status == "finished"`
* `payload.metadata.source == "n8n-competitor-demo"`

### 4. `Get Task Details`
The webhook body includes the `session_id`. We fetch the full task record so we get the `output` field containing the structured JSON from step 2.

### 5. `Code – Generate Slack message`
A short JS snippet turns the JSON into a nicely-formatted Slack block with emojis and bullet points. Feel free to tweak the formatting.

### 6. `HTTP Request – Send to Slack`
Finally we POST the message to your incoming webhook and celebrate 🎉.

---

## Customize as you want

This workflow is just the starting point – Browser Use + n8n gives you endless possibilities. Here are some ideas:

| Want to... | How to do it |
| ---- | ------ |
| **Extract different data** | Edit `structured_output_json` to specify exactly what fields you need (pricing, reviews, contact info, etc.) and adjust the JS formatter. |
| **Send to Teams/Email/Notion** | Swap the last Slack node for Teams, Gmail, or any of n8n's 400+ connectors. |
| **Run automatically** | Replace the Form trigger with a Cron trigger for daily/weekly competitor monitoring. |
| **Monitor multiple competitors** | Use a Google Sheets trigger with a list of companies and loop through them. |
| **Add AI analysis** | Pipe the extracted data through OpenAI/Claude to generate insights and summaries. |
| **Create alerts** | Set up conditional logic to only notify when competitors announce new features or price changes. |
| **Build a dashboard** | Send data to Airtable, Notion, or Google Sheets to build a real-time competitor intelligence dashboard. |

The beauty of Browser Use is that it handles the complex web browsing while you focus on building the perfect workflow for your needs.

---

## Workflow JSON

<Accordion title="n8n Workflow JSON (click to expand)">
```json id="workflow-json"
{
  "name": "Competitor Intelligence Workflow with webhooks",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "get-research-data",
        "options": {}
      },
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [
        -480,
        176
      ],
      "id": "81166dab-eb91-4627-b773-1aa7f7bd86ee",
      "name": "Webhook",
      "webhookId": "025bc4bf-00c0-47d4-bd5f-79046674d017"
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict",
            "version": 2
          },
          "conditions": [
            {
              "id": "8d9701b6-1dc2-4e55-9fe4-ef1735ff1ebc",
              "leftValue": "={{ $json.body.payload.status }}",
              "rightValue": "finished",
              "operator": {
                "type": "string",
                "operation": "equals",
                "name": "filter.operator.equals"
              }
            },
            {
              "id": "7cf18a23-f3d8-4a70-a77c-c286a231fc7f",
              "leftValue": "={{ $json.body.payload.metadata.source }}",
              "rightValue": "n8n-competitor-demo",
              "operator": {
                "type": "string",
                "operation": "equals",
                "name": "filter.operator.equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "type": "n8n-nodes-base.if",
      "typeVersion": 2.2,
      "position": [
        -256,
        176
      ],
      "id": "b38737cc-0b8a-4a76-930f-362eb5de9ef9",
      "name": "If"
    },
    {
      "parameters": {
        "formTitle": "Run Competitor Analysis",
        "formFields": {
          "values": [
            {
              "fieldLabel": "Competitor Name",
              "placeholder": "(e.g. OpenAI)",
              "requiredField": true
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.formTrigger",
      "typeVersion": 2.2,
      "position": [
        -336,
        -64
      ],
      "id": "fcfc33dd-7d8a-460b-838d-955c65416aea",
      "name": "On form submission",
      "webhookId": "b2712d5b-14ae-424b-8733-fe6e77cebd43"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "https://api.browser-use.com/api/v1/run-task",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpBearerAuth",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {}
          ]
        },
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={\n  \"task\": \"Do exhaustive research on {{ $json['Competitor Name'] }} and extract all pricing information, job postings, new features and announcements\",\n  \"save_browser_data\": true,\n  \"structured_output_json\": \"{\\n  \\\"pricing\\\": {\\n    \\\"plans\\\": [\\\"string\\\"],\\n    \\\"prices\\\": [\\\"string\\\"],\\n    \\\"features\\\": [\\\"string\\\"]\\n  },\\n  \\\"jobs\\\": {\\n    \\\"titles\\\": [\\\"string\\\"],\\n    \\\"departments\\\": [\\\"string\\\"],\\n    \\\"locations\\\": [\\\"string\\\"]\\n  },\\n  \\\"new_features\\\": {\\n    \\\"titles\\\": [\\\"string\\\"],\\n    \\\"description\\\": [\\\"string\\\"]\\n  },\\n  \\\"announcements\\\": {\\n    \\\"titles\\\": [\\\"string\\\"],\\n    \\\"description\\\": [\\\"string\\\"]\\n  }\\n}\",\n\"metadata\": {\"source\": \"n8n-competitor-demo\"}\n} ",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        -112,
        -64
      ],
      "id": "d10bef40-e2a3-41ff-a507-4f365c13dc52",
      "name": "BrowserUse Run Task",
      "credentials": {
        "httpBearerAuth": {
          "id": "peg6MzgmJNRMCMnT",
          "name": "Browser-Use API Key"
        }
      }
    },
    {
      "parameters": {
        "url": "=https://api.browser-use.com/api/v1/task/{{ $('Webhook').item.json.body.payload.session_id }}",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpBearerAuth",
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        0,
        144
      ],
      "id": "e49c28ff-11a2-4195-94ab-ca5796572c34",
      "name": "Get Task details",
      "credentials": {
        "httpBearerAuth": {
          "id": "peg6MzgmJNRMCMnT",
          "name": "Browser-Use API Key"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "const output_data = $input.first().json.output;\nconst data = JSON.parse(output_data);\n\nconst pricing = data?.pricing;\nconst jobs = data?.jobs;\nconst newFeatures = data?.new_features;\nconst announcements = data?.announcements;\n\n// Helper function to format arrays as bullet points\nconst formatAsBullets = (arr, prefix = \"• \" => {\n  if (!arr || arr.length === 0) return \"• N/A\";\n  return arr.map(item => `${prefix}${item}`).join(\"\\n\");\n};\n\nreturn {\n  text: `🏷️ *Pricing*\\nPlans:\\n${formatAsBullets(pricing?.plans)}\\n\\nPrices:\\n${formatAsBullets(pricing?.prices)}\\n\\nFeatures:\\n${formatAsBullets(pricing?.features)}\\n\\n💼 *Jobs*\\nTitles:\\n${formatAsBullets(jobs?.titles)}\\n\\nDepartments:\\n${formatAsBullets(jobs?.departments)}\\n\\nLocations:\\n${formatAsBullets(jobs?.locations)}\\n\\n✨ *New Features*\\nTitles:\\n${formatAsBullets(newFeatures?.titles)}\\n\\nDescription:\\n${formatAsBullets(newFeatures?.description)}\\n\\n📢 *Announcements*\\n${formatAsBullets(announcements?.description)}`\n};"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        208,
        144
      ],
      "id": "54bc087d-237d-438a-b688-bcbec25d9c45",
      "name": "Generate Slack message"
    },
    {
      "parameters": {
        "method": "POST",
        "url": "",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "text",
              "value": "={{ $json.text }}"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        432,
        144
      ],
      "id": "969a16f0-677b-4e46-a8bb-57a80b5daf07",
      "name": "Send to Slack"
    }
  ],
  "pinData": {},
  "connections": {
    "Webhook": {
      "main": [
        [
          {
            "node": "If",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If": {
      "main": [
        [
          {
            "node": "Get Task details",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "On form submission": {
      "main": [
        [
          {
            "node": "BrowserUse Run Task",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Task details": {
      "main": [
        [
          {
            "node": "Generate Slack message",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Slack message": {
      "main": [
        [
          {
            "node": "Send to Slack",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "f3b38678-4821-41ad-952c-df9bbba40fc8",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "7a1d1fd830bae2a00010153cf810fd67e0c87b8ae64ceb62273c87183efda365"
  },
  "id": "qmhqkZH8DhISWMmc",
  "tags": []
}
```
</Accordion>

Copy everything between the braces, import into n8n and you're good to go.

<Note>
Having trouble? Ping us in the #integrations channel on [Discord](https://discord.gg/fT6Re4KN) – we’re happy to help.
</Note>
