
<img src="./assets/desktop.png" alt="Browser Use Desktop App" width="full"/>

<br/>

[![GitHub stars](https://img.shields.io/github/stars/browser-use/desktop?style=social)](https://github.com/browser-use/desktop/stargazers)
[![Discord](https://img.shields.io/discord/1303749220842340412?color=7289DA&label=Discord&logo=discord&logoColor=white)](https://link.browser-use.com/discord)
[![Documentation](https://img.shields.io/badge/Documentation-📕-blue)](https://docs.browser-use.com)
[![Browser-Use](https://img.shields.io/twitter/follow/browser_use?style=social)](https://x.com/browser_use)

This project is designed to make websites accessible for AI agents and builds upon the foundation of [browser-use](https://github.com/browser-use/browser-use) + [web-ui](https://github.com/browser-use/web-ui).

**UI:** is built on Electron and Gradio and supports most `browser-use` functionalities. This UI is designed to be user-friendly and enables easy interaction with the browser agent.

**Expanded LLM Support:** We've integrated support for various Large Language Models (LLMs), including: Google, OpenAI, Azure OpenAI, Anthropic, DeepSeek, Ollama etc. And we plan to add support for even more models in the future.

**Custom Browser Support:** The desktop app uses your existing Google Chrome browser, eliminating the need to re-login to sites or deal with other authentication challenges. This feature also supports high-definition screen recording.

<img width="100%" alt="Screenshot of Browser Use Desktop running on macOS" src="https://github.com/user-attachments/assets/94e9fec1-7b17-453e-af6d-2e35f54b46ab" />


## Get Started

```bash
git clone https://github.com/browser-use/desktop
cd desktop

npm install
vite dev
```

## Download

- Download for macOS *(Coming soon...)*
- Download for Windows *(Coming soon...)*
- Download for Linux *(Coming soon...)*
