<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Browser-Use Desktop</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        color: #333;
      }
      h1 {
        margin-bottom: 20px;
        color: #2c3e50;
      }
      main {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      .console-output {
        background-color: #263238;
        color: #eeffff;
        padding: 0;
        border-radius: 5px 5px 0 0;
        height: 300px;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 15;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.3);
        display: flex;
        flex-direction: column;
      }
      
      .console-output.visible {
        transform: translateY(0);
      }
      
      /* Adjust layout for loading state */
      body.loading .console-output.visible {
        height: 250px;
      }
      
      .console-header {
        background-color: #1e272c;
        padding: 8px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #37474F;
      }
      
      .console-header h3 {
        margin: 0;
        font-size: 14px;
        font-weight: normal;
        color: #B0BEC5;
      }
      
      .console-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .console-tabs {
        display: flex;
        gap: 8px;
      }
      
      .console-tab {
        background-color: #37474F;
        border: none;
        color: #B0BEC5;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }
      
      .console-tab.active {
        background-color: #546E7A;
        color: #ECEFF1;
      }
      
      .console-tab:hover {
        background-color: #455A64;
      }
      
      #console-actions {
        background-color: #1e272c;
        padding: 8px 15px;
        display: flex;
        border-bottom: 1px solid #37474F;
      }
      
      .restart-btn {
        background-color: #455A64;
        color: #ECEFF1;
        border: none;
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 12px;
        cursor: pointer;
        margin-right: 10px;
      }
      
      .restart-btn:hover {
        background-color: #546E7A;
      }
      
      .action-row {
        display: flex;
        align-items: center;
        width: 100%;
      }
      
      .command-display {
        margin-left: 15px;
        font-family: monospace;
        color: #81C784;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        font-size: 12px;
      }
      
      .console-content {
        overflow-y: auto;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        flex: 1;
        height: 100%;
      }
      
      .command-line {
        color: #81C784;
        font-weight: bold;
        padding: 5px 0;
        border-bottom: 1px solid #37474F;
        margin-bottom: 10px;
      }
      
      .close-btn {
        font-size: 24px;
        color: #B0BEC5;
        cursor: pointer;
        user-select: none;
        line-height: 1;
      }
      
      .close-btn:hover {
        color: #ECEFF1;
      }
      
      #console-content {
        overflow-y: auto;
        padding: 15px;
        font-family: monospace;
        white-space: pre-wrap;
        flex: 1;
      }
      .console-output .stderr {
        color: #ff5252;
      }
      .console-output .info {
        color: #69f0ae;
      }
      .console-output .error {
        color: #ff7043;
      }
      .loading-container {
        margin: 20px 0;
      }
      .loading-bar {
        height: 20px;
        background-color: #ddd;
        border-radius: 10px;
        overflow: hidden;
      }
      .loading-progress {
        height: 100%;
        background-color: #4caf50;
        width: 0%;
        transition: width 0.3s ease;
      }
      .webview-container {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
      }
      
      .controls {
        position: fixed;
        bottom: 10px;
        right: 10px;
        z-index: 20;
        background-color: rgba(0,0,0,0.6);
        border-radius: 5px;
        padding: 8px;
        display: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        transition: opacity 0.3s ease;
      }
      
      .controls:hover {
        opacity: 1;
      }
      
      .controls button {
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 15px;
        cursor: pointer;
        font-weight: bold;
        transition: background-color 0.2s ease;
      }
      
      .controls button:hover {
        background-color: #0d8aee;
      }
      webview {
        width: 100%;
        height: 100%;
        border: none;
      }
      .launch-button {
        display: none;
        padding: 10px 20px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
        margin-top: 10px;
      }
      .launch-button:hover {
        background-color: #1976d2;
      }
    </style>
  </head>
  <body class="loading">
    <h1>Browser-Use Desktop</h1>
    <main>
      <div id="loading-container" class="loading-container">
        <h3>Starting Python web server...</h3>
        <div class="loading-bar">
          <div id="loading-progress" class="loading-progress"></div>
        </div>
      </div>
      
      <div id="console-output" class="console-output">
        <div class="console-header">
          <h3>Console Output</h3>
          <div class="console-tabs">
            <button id="tab-python" class="console-tab active">Python</button>
            <button id="tab-chrome" class="console-tab">Chrome</button>
          </div>
          <span id="close-console" class="close-btn">×</span>
        </div>
        <div id="console-actions">
          <div class="action-row">
            <button id="restart-python" class="restart-btn">Restart Python</button>
            <div id="python-command" class="command-display"></div>
          </div>
          <div class="action-row" style="display: none;">
            <button id="restart-chrome" class="restart-btn">Restart Chrome</button>
            <div id="chrome-command" class="command-display"></div>
          </div>
        </div>
        <div id="console-content"></div>
        <div id="chrome-console-content" class="console-content" style="display: none;"></div>
      </div>
      
      <button id="launch-button" class="launch-button">Open Web UI</button>
      
      <div id="webview-container" class="webview-container">
        <webview id="webview" src="about:blank" webpreferences="contextIsolation=yes" allowpopups></webview>
      </div>
      
      <div id="controls" class="controls">
        <button id="toggle-console">Show Console</button>
      </div>
    </main>
    
    <script type="module" src="/src/renderer.ts"></script>
  </body>
</html>
